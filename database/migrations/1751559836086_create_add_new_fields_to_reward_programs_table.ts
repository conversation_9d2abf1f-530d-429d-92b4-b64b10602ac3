import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_reward_programs'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add fields for first-time discount program
      table.float('discountAmount').nullable()
      table.string('discountType').nullable() // 'fixed_amount' or 'percentage'

      // Add fields for engagement milestone programs
      table.boolean('isEngagementProgram').defaultTo(false)
      table.boolean('allowMultipleMilestones').defaultTo(false)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('discountAmount')
      table.dropColumn('discountType')
      table.dropColumn('isEngagementProgram')
      table.dropColumn('allowMultipleMilestones')
    })
  }
}
