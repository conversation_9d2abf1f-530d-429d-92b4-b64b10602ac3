import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_user_engagement_milestones'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()

      table.uuid('userId').references('id').inTable('zn_users').onDelete('CASCADE')
      table
        .uuid('milestoneConditionId')
        .references('id')
        .inTable('zn_reward_milestone_conditions')
        .onDelete('CASCADE')

      // Track when milestone was achieved
      table.timestamp('achievedAt', { useTz: true }).notNullable()

      // Track the count when milestone was achieved
      table.integer('countAtAchievement').notNullable()

      // Track if reward has been processed
      table.boolean('rewardProcessed').defaultTo(false)

      table.timestamp('createdAt', { useTz: true }).defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()

      // Unique constraint to prevent duplicate milestone achievements
      table.unique(['userId', 'milestoneConditionId'])

      // Index for performance
      table.index(['userId', 'achievedAt'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
