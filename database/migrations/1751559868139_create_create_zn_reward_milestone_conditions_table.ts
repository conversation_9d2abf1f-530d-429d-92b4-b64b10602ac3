import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_reward_milestone_conditions'

  async up() {
    this.schema.dropTableIfExists(this.tableName)
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()

      table
        .uuid('rewardProgramId')
        .references('id')
        .inTable('zn_reward_programs')
        .onDelete('CASCADE')

      // Engagement type: view, like, comment, order, sale, store, post
      table.string('engagementType').notNullable()

      // Count threshold to reach for this milestone
      table.integer('count').notNullable()

      // Points to award when milestone is reached
      table.integer('points').notNullable()

      // Optional reward value (for money rewards)
      table.float('rewardValue').nullable()

      // Order for multiple milestones
      table.integer('orderBy').defaultTo(0)

      table.timestamp('createdAt', { useTz: true }).defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()

      // Index for performance
      table.index(['rewardProgramId', 'engagementType'], 'idx_milestone_program_engagement')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
