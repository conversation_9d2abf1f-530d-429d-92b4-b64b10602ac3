import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_users'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Track first-time app usage for discount eligibility
      table.timestamp('firstAppUsageAt', { useTz: true }).nullable()
      table.timestamp('firstOnlinePurchaseAt', { useTz: true }).nullable()
      table.timestamp('firstPosPurchaseAt', { useTz: true }).nullable()

      // Track if first-time discount has been applied
      table.boolean('firstTimeDiscountApplied').defaultTo(false)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('firstAppUsageAt')
      table.dropColumn('firstOnlinePurchaseAt')
      table.dropColumn('firstPosPurchaseAt')
      table.dropColumn('firstTimeDiscountApplied')
    })
  }
}
