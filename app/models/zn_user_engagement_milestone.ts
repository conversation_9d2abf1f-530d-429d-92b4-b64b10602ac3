import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import AppModel from '#models/app_model'
import ZnUser from './zn_user.js'
import ZnRewardMilestoneCondition from './zn_reward_milestone_condition.js'

export default class ZnUserEngagementMilestone extends AppModel {
  static table = 'zn_user_engagement_milestones'

  @column({ columnName: 'userId' })
  declare userId: string

  @belongsTo(() => ZnUser, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof ZnUser>

  @column({ columnName: 'milestoneConditionId' })
  declare milestoneConditionId: string

  @belongsTo(() => ZnRewardMilestoneCondition, {
    foreignKey: 'milestoneConditionId',
  })
  declare milestoneCondition: BelongsTo<typeof ZnRewardMilestoneCondition>

  @column.dateTime({ columnName: 'achievedAt' })
  declare achievedAt: DateTime

  @column({ columnName: 'countAtAchievement' })
  declare countAtAchievement: number

  @column({ columnName: 'rewardProcessed' })
  declare rewardProcessed: boolean
}
