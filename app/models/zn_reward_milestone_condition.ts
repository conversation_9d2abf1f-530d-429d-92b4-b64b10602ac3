import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import AppModel from '#models/app_model'
import ZnRewardProgram from './zn_reward_program.js'

export enum EEngagementType {
  VIEW_POST = 'view_post',
  VIEW_STORE = 'view_store',
  LIKE_POST = 'like_post',
  COMMENT_POST = 'comment_post',
  ORDER = 'order',
  SALE = 'sale',
  STORE_VISIT = 'store_visit',
  POST_CREATE = 'post_create',
}

export default class ZnRewardMilestoneCondition extends AppModel {
  static table = 'zn_reward_milestone_conditions'

  @column({ columnName: 'rewardProgramId' })
  declare rewardProgramId: string

  @belongsTo(() => ZnRewardProgram, {
    foreignKey: 'rewardProgramId',
  })
  declare rewardProgram: BelongsTo<typeof ZnRewardProgram>

  @column({ columnName: 'engagementType' })
  declare engagementType: EEngagementType

  @column({ columnName: 'count' })
  declare count: number

  @column({ columnName: 'points' })
  declare points: number

  @column({ columnName: 'rewardValue' })
  declare rewardValue: number | null

  @column({ columnName: 'orderBy' })
  declare orderBy: number
}
