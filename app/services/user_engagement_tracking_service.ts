import { TRACKING_ACTION } from '#constants/tracking'
import ZnRewardMilestoneCondition, { EEngagementType } from '#models/zn_reward_milestone_condition'
import ZnRewardProgram, {
  ERewardProgramStatus,
  ERewardProgramType,
  EProgramRewardType,
} from '#models/zn_reward_program'
import ZnUserEngagementMilestone from '#models/zn_user_engagement_milestone'
import ZnUser from '#models/zn_user'
import ZnTracking from '#models/zn_tracking'
import db from '@adonisjs/lucid/services/db'
import { RewardService } from './reward_service.js'
import { DateTime } from 'luxon'

export class UserEngagementTrackingService {
  private rewardService = new RewardService()

  /**
   * Map TRACKING_ACTION to EEngagementType
   */
  private trackingActionToEngagementType: Record<TRACKING_ACTION, EEngagementType | null> = {
    [TRACKING_ACTION.VIEW_POST]: EEngagementType.VIEW_POST,
    [TRACKING_ACTION.VIEW_STORE]: EEngagementType.VIEW_STORE,
    [TRACKING_ACTION.ADD_WISHLIST]: EEngagementType.LIKE_POST, // ADD_WISHLIST is used for likes
    [TRACKING_ACTION.CLICK_CALL_ON_POST]: EEngagementType.STORE_VISIT,
    [TRACKING_ACTION.VIEW_PRODUCT]: null, // Not mapped to engagement milestones
    [TRACKING_ACTION.CLICK_CALL_ON_STORE]: null,
    [TRACKING_ACTION.OPEN_APP]: null,
    [TRACKING_ACTION.CLOSE_APP]: null,
    [TRACKING_ACTION.RATING_PRODUCT]: null,
    [TRACKING_ACTION.FAVORITE_PRODUCT]: null,
    [TRACKING_ACTION.ADD_TO_CART]: null,
    [TRACKING_ACTION.SHARE]: null,
    [TRACKING_ACTION.GET_STREAM_VIEW]: null,
    [TRACKING_ACTION.SEARCH]: null,
    [TRACKING_ACTION.LIKE_POST]: EEngagementType.LIKE_POST,
    [TRACKING_ACTION.COMMENT_POST]: EEngagementType.COMMENT_POST,
    [TRACKING_ACTION.ORDER]: EEngagementType.ORDER,
    [TRACKING_ACTION.SALE]: EEngagementType.SALE,
    [TRACKING_ACTION.STORE_VISIT]: EEngagementType.STORE_VISIT,
    [TRACKING_ACTION.POST_CREATE]: EEngagementType.POST_CREATE,
  }

  /**
   * Check and process engagement milestones for a user action
   */
  async checkEngagementMilestones(payload: {
    userId: string
    action: TRACKING_ACTION
    resourceId: string
  }): Promise<void> {
    const engagementType = this.trackingActionToEngagementType[payload.action]

    if (!engagementType) {
      return // This action doesn't trigger engagement milestones
    }

    // Get all active engagement milestone programs
    const engagementPrograms = await ZnRewardProgram.query()
      .where('programType', ERewardProgramType.ENGAGEMENT_MILESTONE)
      .where('rewardType', EProgramRewardType.EARN)
      .where('status', ERewardProgramStatus.ACTIVE)
      .where('isEngagementProgram', true)
      .preload('milestoneConditions', (query) => {
        query
          .where('engagementType', engagementType)
          .orderBy('orderBy', 'asc')
          .orderBy('count', 'asc')
      })

    if (engagementPrograms.length === 0) {
      return // No active engagement programs for this action
    }

    // Get current user engagement count for this action
    const currentCount = await this.getUserEngagementCount(payload.userId, payload.action)

    // Process each engagement program
    for (const program of engagementPrograms) {
      await this.processEngagementProgram(program, payload.userId, engagementType, currentCount)
    }
  }

  /**
   * Process a specific engagement program for milestone achievements
   */
  private async processEngagementProgram(
    program: ZnRewardProgram,
    userId: string,
    engagementType: EEngagementType,
    currentCount: number
  ): Promise<void> {
    const milestoneConditions = program.milestoneConditions

    for (const condition of milestoneConditions) {
      // Check if user has reached this milestone count
      if (currentCount >= condition.count) {
        // Check if user has already achieved this milestone
        const existingMilestone = await ZnUserEngagementMilestone.query()
          .where('userId', userId)
          .where('milestoneConditionId', condition.id)
          .first()

        if (!existingMilestone) {
          // User achieved this milestone for the first time
          await this.awardMilestoneReward(userId, condition, currentCount)
        }
      }

      // If program doesn't allow multiple milestones, only check the first achievable one
      if (!program.allowMultipleMilestones && currentCount >= condition.count) {
        break
      }
    }
  }

  /**
   * Award reward for achieving a milestone
   */
  private async awardMilestoneReward(
    userId: string,
    condition: ZnRewardMilestoneCondition,
    currentCount: number
  ): Promise<void> {
    // Create milestone achievement record
    await ZnUserEngagementMilestone.create({
      userId,
      milestoneConditionId: condition.id,
      achievedAt: DateTime.now(),
      countAtAchievement: currentCount,
      rewardProcessed: false,
    })

    // Award points using the reward service
    if (condition.points > 0) {
      await this.rewardService.adjustPointsByProgramId({
        userId,
        rewardProgramId: condition.rewardProgramId,
        variableRewardValue: condition.rewardValue || undefined,
      })

      // Mark reward as processed
      await ZnUserEngagementMilestone.query()
        .where('userId', userId)
        .where('milestoneConditionId', condition.id)
        .update({ rewardProcessed: true })
    }
  }

  /**
   * Get user's current engagement count for a specific action
   */
  private async getUserEngagementCount(userId: string, action: TRACKING_ACTION): Promise<number> {
    const result = await ZnTracking.query()
      .where('userId', userId)
      .where('action', action)
      .count('* as count')
      .first()

    return result ? parseInt(result.$extras.count) : 0
  }

  /**
   * Get user's engagement milestone progress
   */
  async getUserMilestoneProgress(userId: string): Promise<any[]> {
    const engagementPrograms = await ZnRewardProgram.query()
      .where('programType', ERewardProgramType.ENGAGEMENT_MILESTONE)
      .where('status', ERewardProgramStatus.ACTIVE)
      .where('isEngagementProgram', true)
      .preload('milestoneConditions', (query) => {
        query.orderBy('orderBy', 'asc').orderBy('count', 'asc')
      })

    const progress = []

    for (const program of engagementPrograms) {
      for (const condition of program.milestoneConditions) {
        const engagementAction = this.getTrackingActionByEngagementType(condition.engagementType)
        if (!engagementAction) continue

        const currentCount = await this.getUserEngagementCount(userId, engagementAction)

        const achievement = await ZnUserEngagementMilestone.query()
          .where('userId', userId)
          .where('milestoneConditionId', condition.id)
          .first()

        progress.push({
          programId: program.id,
          programName: program.rewardName,
          conditionId: condition.id,
          engagementType: condition.engagementType,
          requiredCount: condition.count,
          currentCount,
          points: condition.points,
          rewardValue: condition.rewardValue,
          isAchieved: !!achievement,
          achievedAt: achievement?.achievedAt,
          progress: Math.min((currentCount / condition.count) * 100, 100),
        })
      }
    }

    return progress
  }

  /**
   * Get TRACKING_ACTION by EEngagementType (reverse mapping)
   */
  private getTrackingActionByEngagementType(
    engagementType: EEngagementType
  ): TRACKING_ACTION | null {
    for (const [action, type] of Object.entries(this.trackingActionToEngagementType)) {
      if (type === engagementType) {
        return parseInt(action) as TRACKING_ACTION
      }
    }
    return null
  }

  /**
   * Manually trigger milestone check for specific engagement types
   * Useful for actions not directly tracked by TRACKING_ACTION (like comments, orders, sales)
   */
  async triggerMilestoneCheck(payload: {
    userId: string
    engagementType: EEngagementType
    resourceId?: string
  }): Promise<void> {
    // Get current count for this engagement type
    let currentCount = 0

    switch (payload.engagementType) {
      case EEngagementType.COMMENT_POST:
        // Count user's comments on posts
        currentCount = await db
          .from('zn_post_comments')
          .where('userId', payload.userId)
          .whereNull('deletedAt')
          .count('* as count')
          .then((result) => parseInt(result[0].count))
        break

      case EEngagementType.ORDER:
        // Count user's orders
        currentCount = await db
          .from('zn_orders')
          .where('userId', payload.userId)
          .whereNull('deletedAt')
          .count('* as count')
          .then((result) => parseInt(result[0].count))
        break

      case EEngagementType.SALE:
        // Count user's sales (if they are a vendor/store owner)
        currentCount = await db
          .from('zn_orders')
          .join('zn_order_details', 'zn_orders.id', 'zn_order_details.orderId')
          .join('zn_product_variants', 'zn_order_details.variantId', 'zn_product_variants.id')
          .join('zn_products', 'zn_product_variants.productId', 'zn_products.id')
          .join('zn_stores', 'zn_products.storeId', 'zn_stores.id')
          .where('zn_stores.userId', payload.userId)
          .whereNull('zn_orders.deletedAt')
          .count('* as count')
          .then((result) => parseInt(result[0].count))
        break

      case EEngagementType.POST_CREATE:
        // Count user's created posts
        currentCount = await db
          .from('zn_posts')
          .where('userId', payload.userId)
          .whereNull('deletedAt')
          .count('* as count')
          .then((result) => parseInt(result[0].count))
        break

      default:
        // For other types, try to get from tracking
        const trackingAction = this.getTrackingActionByEngagementType(payload.engagementType)
        if (trackingAction) {
          currentCount = await this.getUserEngagementCount(payload.userId, trackingAction)
        }
        break
    }

    // Get all active engagement milestone programs for this type
    const engagementPrograms = await ZnRewardProgram.query()
      .where('programType', ERewardProgramType.ENGAGEMENT_MILESTONE)
      .where('rewardType', EProgramRewardType.EARN)
      .where('status', ERewardProgramStatus.ACTIVE)
      .where('isEngagementProgram', true)
      .preload('milestoneConditions', (query) => {
        query
          .where('engagementType', payload.engagementType)
          .orderBy('orderBy', 'asc')
          .orderBy('count', 'asc')
      })

    // Process each engagement program
    for (const program of engagementPrograms) {
      await this.processEngagementProgram(
        program,
        payload.userId,
        payload.engagementType,
        currentCount
      )
    }
  }
}
