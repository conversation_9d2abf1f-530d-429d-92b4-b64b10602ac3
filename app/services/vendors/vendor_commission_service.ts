import { EApprovalStatus } from '#constants/approval_status'
import Zn<PERSON>rder from '#models/zn_order'
import ZnOrderDetail from '#models/zn_order_detail'
import ZnVendor from '#models/zn_vendor'
import ZnVendorCommission from '#models/zn_vendor_commission'
import logger from '@adonisjs/core/services/logger'
import { UserEngagementTrackingService } from '#services/user_engagement_tracking_service'
import { TRACKING_ACTION } from '#constants/tracking'

export default class VendorCommissionService {
  async getAllCommisisons(page: number = 1, limit: number = 10): Promise<ZnVendorCommission[]> {
    const query = ZnVendorCommission.query().preload('order').preload('vendor')

    return query.paginate(page, limit)
  }
  async getAllCommisisonsByVendor(
    vendorId: string,
    page: number = 1,
    limit: number = 10
  ): Promise<ZnVendorCommission[]> {
    const query = ZnVendorCommission.query()
      .where('vendorId', vendorId)
      .preload('order')
      .preload('vendor')

    return query.paginate(page, limit)
  }

  async getCommissionById(id: string): Promise<ZnVendorCommission> {
    const commission = await ZnVendorCommission.query()
      .where('id', id)
      .preload('order')
      .preload('vendor')
      .firstOrFail()
    return commission
  }

  async update(id: string, data: Partial<ZnVendorCommission>): Promise<ZnVendorCommission> {
    const commission = await ZnVendorCommission.query().where('id', id).firstOrFail()
    commission.merge(data)
    await commission.save()

    await commission.load('order')
    await commission.load('vendor')
    return commission
  }

  async delete(id: string) {
    const commission = await ZnVendorCommission.find(id)
    if (!commission) return
    await commission.softDelete()
  }

  async calculateCommission(shopifyOrder: any): Promise<void> {
    const order = await ZnOrder.query()
      .where('shopifyId', shopifyOrder.admin_graphql_api_id)
      .first()

    if (!order) {
      logger.debug('No order provided for commission calculation.')
      return
    }

    const orderDetails = await ZnOrderDetail.query()
      .where('orderId', order.id)
      .preload('variant', (query) => {
        query.preload('product', (productQuery) => {
          productQuery.preload('vendor')
        })
      })

    const orderDetailsByVendor: Record<string, ZnOrderDetail[]> = {}
    for (const orderDetail of orderDetails) {
      const vendorId = orderDetail.variant?.product?.vendor?.id
      if (vendorId) {
        if (!orderDetailsByVendor[vendorId]) {
          orderDetailsByVendor[vendorId] = []
        }
        orderDetailsByVendor[vendorId].push(orderDetail)
      }
    }

    for (const vendorId in orderDetailsByVendor) {
      const details = orderDetailsByVendor[vendorId]
      await this.createVendorCommission(vendorId, details)
    }
  }

  private async createVendorCommission(
    vendorId: string,
    orderDetails: ZnOrderDetail[]
  ): Promise<void> {
    const vendor = await ZnVendor.query().where('id', vendorId).firstOrFail()

    let totalCommission = 0

    for (const detail of orderDetails) {
      const commissionAmount = detail.price * detail.quantity * vendor.commissionRate
      totalCommission += commissionAmount + vendor.fixedCommissionAmount
    }

    if (totalCommission > 0) {
      const commissionData = {
        vendorId: vendor.id,
        orderId: orderDetails[0].orderId,
        commissionRate: vendor.commissionRate,
        fixedCommissionAmount: vendor.fixedCommissionAmount,
        commissionAmount: totalCommission,
        status: EApprovalStatus.PENDING,
      }
      await ZnVendorCommission.create(commissionData)
      logger.info(
        `Vendor commission created for vendor ${vendor.companyName} with total amount: ${totalCommission}`
      )

      // Track engagement milestone for sale (vendor gets commission)
      if (vendor.userId) {
        try {
          const engagementService = new UserEngagementTrackingService()
          await engagementService.triggerMilestoneCheck(vendor.userId, TRACKING_ACTION.SALE, {
            resourceId: orderDetails[0].orderId,
            resourceType: 'ZnOrder',
          })
        } catch (engagementError) {
          console.error('Engagement tracking error:', engagementError)
          // Don't fail the main operation if engagement tracking fails
        }
      }
    }
  }
}
