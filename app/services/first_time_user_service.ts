import ZnRewardProgram, { ERewardProgramStatus, ERewardProgramType, EProgramRewardType, EDiscountType } from '#models/zn_reward_program'
import ZnUser from '#models/zn_user'
import ZnOrder from '#models/zn_order'
import { DateTime } from 'luxon'
import { RewardService } from './reward_service.js'

export enum EFirstTimeUsageType {
  APP_USAGE = 'app_usage',
  ONLINE_PURCHASE = 'online_purchase',
  POS_PURCHASE = 'pos_purchase',
}

export interface IFirstTimeDiscountResult {
  applied: boolean
  discountAmount?: number
  discountType?: EDiscountType
  programId?: string
  programName?: string
  message?: string
}

export class FirstTimeUserService {
  private rewardService = new RewardService()

  /**
   * Check and apply first-time app usage discount
   */
  async checkFirstTimeAppUsage(userId: string): Promise<IFirstTimeDiscountResult> {
    const user = await ZnUser.find(userId)
    if (!user) {
      return { applied: false, message: 'User not found' }
    }

    // Check if user has already used the app before
    if (user.firstAppUsageAt) {
      return { applied: false, message: 'User has already used the app before' }
    }

    // Mark first app usage
    await user.merge({ firstAppUsageAt: DateTime.now() }).save()

    // Apply first-time discount if available
    return await this.applyFirstTimeDiscount(user, EFirstTimeUsageType.APP_USAGE)
  }

  /**
   * Check and apply first-time online purchase discount
   */
  async checkFirstTimeOnlinePurchase(userId: string, orderId?: string): Promise<IFirstTimeDiscountResult> {
    const user = await ZnUser.find(userId)
    if (!user) {
      return { applied: false, message: 'User not found' }
    }

    // Check if user has already made an online purchase before
    if (user.firstOnlinePurchaseAt) {
      return { applied: false, message: 'User has already made an online purchase before' }
    }

    // Mark first online purchase
    await user.merge({ firstOnlinePurchaseAt: DateTime.now() }).save()

    // Apply first-time discount if available
    return await this.applyFirstTimeDiscount(user, EFirstTimeUsageType.ONLINE_PURCHASE, orderId)
  }

  /**
   * Check and apply first-time POS purchase discount
   */
  async checkFirstTimePosPurchase(userId: string, orderId?: string): Promise<IFirstTimeDiscountResult> {
    const user = await ZnUser.find(userId)
    if (!user) {
      return { applied: false, message: 'User not found' }
    }

    // Check if user has already made a POS purchase before
    if (user.firstPosPurchaseAt) {
      return { applied: false, message: 'User has already made a POS purchase before' }
    }

    // Mark first POS purchase
    await user.merge({ firstPosPurchaseAt: DateTime.now() }).save()

    // Apply first-time discount if available
    return await this.applyFirstTimeDiscount(user, EFirstTimeUsageType.POS_PURCHASE, orderId)
  }

  /**
   * Apply first-time discount based on usage type
   */
  private async applyFirstTimeDiscount(
    user: ZnUser,
    usageType: EFirstTimeUsageType,
    orderId?: string
  ): Promise<IFirstTimeDiscountResult> {
    // Check if user has already received first-time discount
    if (user.firstTimeDiscountApplied) {
      return { applied: false, message: 'User has already received first-time discount' }
    }

    // Get active first-time discount programs
    const discountPrograms = await ZnRewardProgram.query()
      .where('programType', ERewardProgramType.FIRST_TIME_DISCOUNT)
      .where('rewardType', EProgramRewardType.EARN)
      .where('status', ERewardProgramStatus.ACTIVE)
      .whereNotNull('discountAmount')
      .whereNotNull('discountType')

    if (discountPrograms.length === 0) {
      return { applied: false, message: 'No active first-time discount programs available' }
    }

    // Use the first available discount program
    const program = discountPrograms[0]

    // Apply the discount
    const result = await this.processFirstTimeDiscount(user, program, usageType, orderId)

    if (result.applied) {
      // Mark that user has received first-time discount
      await user.merge({ firstTimeDiscountApplied: true }).save()
    }

    return result
  }

  /**
   * Process the actual first-time discount application
   */
  private async processFirstTimeDiscount(
    user: ZnUser,
    program: ZnRewardProgram,
    usageType: EFirstTimeUsageType,
    orderId?: string
  ): Promise<IFirstTimeDiscountResult> {
    try {
      // Award points through the reward service
      const activity = await this.rewardService.adjustPointsByProgramId({
        userId: user.id,
        rewardProgramId: program.id,
        variableRewardValue: program.discountAmount || undefined,
      })

      if (activity) {
        return {
          applied: true,
          discountAmount: program.discountAmount || 0,
          discountType: program.discountType || EDiscountType.FIXED_AMOUNT,
          programId: program.id,
          programName: program.rewardName,
          message: `First-time ${usageType.replace('_', ' ')} discount applied successfully`,
        }
      } else {
        return {
          applied: false,
          message: 'Failed to process discount through reward service',
        }
      }
    } catch (error) {
      console.error('Error processing first-time discount:', error)
      return {
        applied: false,
        message: 'Error occurred while processing discount',
      }
    }
  }

  /**
   * Check if user is eligible for first-time discounts
   */
  async checkFirstTimeEligibility(userId: string): Promise<{
    appUsage: boolean
    onlinePurchase: boolean
    posPurchase: boolean
    discountAlreadyApplied: boolean
  }> {
    const user = await ZnUser.find(userId)
    if (!user) {
      return {
        appUsage: false,
        onlinePurchase: false,
        posPurchase: false,
        discountAlreadyApplied: false,
      }
    }

    return {
      appUsage: !user.firstAppUsageAt,
      onlinePurchase: !user.firstOnlinePurchaseAt,
      posPurchase: !user.firstPosPurchaseAt,
      discountAlreadyApplied: user.firstTimeDiscountApplied,
    }
  }

  /**
   * Get available first-time discount programs
   */
  async getAvailableFirstTimeDiscounts(): Promise<ZnRewardProgram[]> {
    return await ZnRewardProgram.query()
      .where('programType', ERewardProgramType.FIRST_TIME_DISCOUNT)
      .where('rewardType', EProgramRewardType.EARN)
      .where('status', ERewardProgramStatus.ACTIVE)
      .whereNotNull('discountAmount')
      .whereNotNull('discountType')
  }

  /**
   * Reset first-time usage tracking for a user (admin function)
   */
  async resetFirstTimeTracking(userId: string): Promise<boolean> {
    try {
      const user = await ZnUser.find(userId)
      if (!user) {
        return false
      }

      await user.merge({
        firstAppUsageAt: null,
        firstOnlinePurchaseAt: null,
        firstPosPurchaseAt: null,
        firstTimeDiscountApplied: false,
      }).save()

      return true
    } catch (error) {
      console.error('Error resetting first-time tracking:', error)
      return false
    }
  }

  /**
   * Get user's first-time usage history
   */
  async getUserFirstTimeHistory(userId: string): Promise<{
    firstAppUsageAt: DateTime | null
    firstOnlinePurchaseAt: DateTime | null
    firstPosPurchaseAt: DateTime | null
    firstTimeDiscountApplied: boolean
  } | null> {
    const user = await ZnUser.find(userId)
    if (!user) {
      return null
    }

    return {
      firstAppUsageAt: user.firstAppUsageAt,
      firstOnlinePurchaseAt: user.firstOnlinePurchaseAt,
      firstPosPurchaseAt: user.firstPosPurchaseAt,
      firstTimeDiscountApplied: user.firstTimeDiscountApplied,
    }
  }

  /**
   * Automatically detect and process first-time purchase based on order
   */
  async processOrderForFirstTimeDiscount(orderId: string): Promise<IFirstTimeDiscountResult> {
    try {
      const order = await ZnOrder.query()
        .where('id', orderId)
        .preload('user')
        .first()

      if (!order || !order.user) {
        return { applied: false, message: 'Order or user not found' }
      }

      // Determine if this is an online or POS purchase based on order properties
      // This logic may need to be adjusted based on how you differentiate online vs POS orders
      const isPosOrder = order.source === 'pos' || order.channel === 'pos' // Adjust based on your order schema
      
      if (isPosOrder) {
        return await this.checkFirstTimePosPurchase(order.user.id, orderId)
      } else {
        return await this.checkFirstTimeOnlinePurchase(order.user.id, orderId)
      }
    } catch (error) {
      console.error('Error processing order for first-time discount:', error)
      return { applied: false, message: 'Error processing order' }
    }
  }
}
