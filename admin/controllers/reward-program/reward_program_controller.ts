import ZnRewardProgram, {
  ERewardProgramStatus,
  ERewardProgramType,
} from '#models/zn_reward_program'
import ZnRewardMilestoneCondition from '#models/zn_reward_milestone_condition'
import { HttpContext } from '@adonisjs/core/http'
import { createRewardProgramValidator } from '../../validators/reward-program/reward_program_validator.js'
import db from '@adonisjs/lucid/services/db'

export default class AdminRewardProgramController {
  public async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 5, rewardType } = request.all()

      const query = ZnRewardProgram.query()

      if (rewardType) {
        query.where({ rewardType })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)
      return response.internalServerError('Something went wrong!')
    }
  }

  public async show({ params, response }: HttpContext) {
    try {
      const programId = params.id

      const program = await ZnRewardProgram.query()
        .where('id', programId)
        .preload('milestoneConditions', (query) => {
          query.orderBy('orderBy', 'asc').orderBy('count', 'asc')
        })
        .first()

      if (!program) {
        return response.notFound('Reward Program Not Found')
      }

      return response.ok(program)
    } catch (error) {
      console.log(error)
      return response.internalServerError('Something went wrong!')
    }
  }

  public async store({ request, response }: HttpContext) {
    const data = request.all()
    const payload = await createRewardProgramValidator.validate(data)

    try {
      if (payload.status == ERewardProgramStatus.ACTIVE) {
        const existingProgram = await ZnRewardProgram.query()
          .where({
            status: ERewardProgramStatus.ACTIVE,
            programType: payload.programType,
          })
          .first()

        if (existingProgram) {
          return response.badRequest('A similar program is active!')
        }
      }

      // Use transaction to ensure data consistency
      const result = await db.transaction(async (trx) => {
        // Extract milestone conditions from payload
        const { milestoneConditions, ...programData } = payload

        // Create the reward program
        const program = await ZnRewardProgram.create(programData, { client: trx })

        // Create milestone conditions if this is an engagement milestone program
        if (
          program.programType === ERewardProgramType.ENGAGEMENT_MILESTONE &&
          milestoneConditions
        ) {
          for (const [index, condition] of milestoneConditions.entries()) {
            await ZnRewardMilestoneCondition.create(
              {
                rewardProgramId: program.id,
                engagementType: condition.engagementType,
                count: condition.count,
                points: condition.points,
                rewardValue: condition.rewardValue || null,
                orderBy: condition.orderBy || index + 1,
              },
              { client: trx }
            )
          }
        }

        return program
      })

      // Load the program with milestone conditions for response
      const programWithConditions = await ZnRewardProgram.query()
        .where('id', result.id)
        .preload('milestoneConditions', (query) => {
          query.orderBy('orderBy', 'asc').orderBy('count', 'asc')
        })
        .first()

      return response.ok(programWithConditions)
    } catch (error) {
      console.log(error)
      return response.internalServerError('Something went wrong!')
    }
  }

  public async update({ params, request, response }: HttpContext) {
    const data = request.all()
    const payload = await createRewardProgramValidator.validate(data)

    try {
      const programId = params.id
      const program = await ZnRewardProgram.find(programId)

      if (!program) {
        return response.notFound('Reward Program Not Found')
      }

      if (payload.status == ERewardProgramStatus.ACTIVE) {
        const existingProgram = await ZnRewardProgram.query()
          .where({
            status: ERewardProgramStatus.ACTIVE,
            programType: payload.programType,
          })
          .whereNot({ id: programId })
          .first()

        if (existingProgram) {
          return response.badRequest('A similar program is active!')
        }
      }

      // Use transaction to ensure data consistency
      const result = await db.transaction(async (trx) => {
        // Extract milestone conditions from payload
        const { milestoneConditions, ...programData } = payload

        // Update the reward program
        await program.merge(programData).save({ client: trx })

        // Handle milestone conditions for engagement milestone programs
        if (program.programType === ERewardProgramType.ENGAGEMENT_MILESTONE) {
          // Delete existing milestone conditions
          await ZnRewardMilestoneCondition.query({ client: trx })
            .where('rewardProgramId', program.id)
            .delete()

          // Create new milestone conditions if provided
          if (milestoneConditions) {
            for (const [index, condition] of milestoneConditions.entries()) {
              await ZnRewardMilestoneCondition.create(
                {
                  rewardProgramId: program.id,
                  engagementType: condition.engagementType,
                  count: condition.count,
                  points: condition.points,
                  rewardValue: condition.rewardValue || null,
                  orderBy: condition.orderBy || index + 1,
                },
                { client: trx }
              )
            }
          }
        }

        return program
      })

      // Load the program with milestone conditions for response
      const programWithConditions = await ZnRewardProgram.query()
        .where('id', result.id)
        .preload('milestoneConditions', (query) => {
          query.orderBy('orderBy', 'asc').orderBy('count', 'asc')
        })
        .first()

      return response.ok(programWithConditions)
    } catch (error) {
      console.log(error)
      return response.internalServerError('Something went wrong!')
    }
  }

  public async destroy({ params, response }: HttpContext) {
    try {
      const programId = params.id
      const program = await ZnRewardProgram.find(programId)

      if (!program) {
        return response.notFound('Reward Program Not Found')
      }

      await program.softDelete()

      return response.ok(program)
    } catch (error) {
      console.log(error)
      return response.internalServerError('Something went wrong!')
    }
  }
}
