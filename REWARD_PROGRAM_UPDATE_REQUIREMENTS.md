# Tài liệu Yêu Cầu Cập Nhật T<PERSON>h <PERSON>ăng "Reward Program"

## 1. Tổng Quan

Tài liệu này mô tả các yêu cầu để cập nhật và hoàn thiện tính năng "Reward Program" trong hệ thống. Implementation hiện tại đã có nền tảng vững chắc nhưng tồn tại một lỗi logic nghiêm trọng và một vài điểm cần cải thiện để đảm bả<PERSON> t<PERSON>h đúng đắn, dễ sử dụng và khả năng mở rộng.

**Mục tiêu:**

1.  Sửa lỗi logic nghiêm trọng liên quan đến việc tính điểm thưởng cho các cột mốc tương tác.
2.  Đồng bộ hóa giao diện người dùng (UI) với logic phía backend để tránh gây nhầm lẫn cho quản trị viên.
3.  <PERSON><PERSON><PERSON> ra các đề xuất về hiệu năng để hệ thống có thể mở rộng trong tương lai.

## 2. Yêu Cầu Tính Năng

Hệ thống cần hỗ trợ hai kịch bản trao thưởng chính:

### Use Case 1: Tặng Thưởng Cho Người Dùng Lần Đầu

- **Mô tả:** Tặng một khoản tiền chiết khấu (discount) X$ cho khách hàng lần đầu tiên thực hiện một trong các hành động sau:
  - Sử dụng ứng dụng.
  - Mua hàng online.
  - Thanh toán tại cửa hàng (POS).
- **Quy tắc:** Mỗi người dùng chỉ được nhận loại thưởng này một lần duy nhất, ngay cả khi có nhiều chương trình đang hoạt động.
- **Admin Setup:** Admin có thể tạo chương trình loại "First time using app", thiết lập số tiền giảm giá, và trạng thái (Active/Disabled).

### Use Case 2: Tặng Thưởng Theo Cột Mốc Tương Tác

- **Mô tả:** Tặng điểm thưởng (points) cho người dùng khi họ đạt được các cột mốc tương tác cụ thể. Các loại tương tác bao gồm: `view`, `like`, `comment`, `order`, `sale`, `store`, `post`.
- **Kịch bản 1 (TH1 - Cố định):** Admin thiết lập một mốc duy nhất với số điểm thưởng cố định (ví dụ: Đạt 1000 lượt xem -> được 100 điểm).
- **Kịch bản 2 (TH2 - Nhiều mốc):** Admin có thể thiết lập nhiều mốc thưởng khác nhau cho cùng một loại tương tác (ví dụ: 1000 lượt xem -> 100 điểm; 5000 lượt xem -> thêm 200 điểm).
- **Admin Setup:** Admin có thể tạo chương trình loại "Count view" (hoặc các loại tương tác khác), và thêm các "Điều kiện bổ sung" (Additional Conditions) để định nghĩa các mốc (`count`) và điểm thưởng (`points`) tương ứng.

## 3. Phân Tích Implementation Hiện Tại (As-Is)

Implementation hiện tại được đánh giá tốt về mặt cấu trúc và tuân thủ các best practice.

### 3.1. Cấu trúc Database (Migrations)

- `zn_reward_programs`: Đã bổ sung các trường `discountAmount`, `discountType`, `isEngagementProgram`, `allowMultipleMilestones`. **Đánh giá: Tốt.**
- `zn_reward_milestone_conditions`: Bảng được thiết kế tốt để lưu các điều kiện cột mốc, bao gồm `engagementType`, `count`, `points`, `rewardValue`. **Đánh giá: Tốt.**
- `zn_users`: Đã thêm các trường để theo dõi hành vi lần đầu của người dùng (`firstAppUsageAt`, `firstTimeDiscountApplied`). **Đánh giá: Tốt.**
- `zn_user_engagement_milestones`: Thiết kế xuất sắc để ghi lại các cột mốc người dùng đã đạt được, sử dụng `UNIQUE KEY` (`userId`, `milestoneConditionId`) để chống trùng lặp. **Đánh giá: Xuất sắc.**

### 3.2. API & Validation (Admin)

- `admin/validators/reward-program/reward_program_validator.ts`: Validator rất chi tiết, sử dụng `requiredWhen` để kiểm tra các trường phụ thuộc và xác thực cấu trúc của mảng `milestoneConditions`. **Đánh giá: Tốt.**
- `admin/controllers/reward-program/reward_program_controller.ts`: Sử dụng `db.transaction` để đảm bảo tính toàn vẹn dữ liệu khi tạo/cập nhật chương trình và các điều kiện đi kèm. Logic xử lý (xóa-rồi-tạo-mới) cho việc cập nhật các điều kiện là hợp lý và an toàn. **Đánh giá: Xuất sắc.**

### 3.3. Logic Nghiệp Vụ (Services)

- `app/services/first_time_user_service.ts`: Đóng gói tốt logic cho việc tặng thưởng người dùng lần đầu. Đã kiểm tra các điều kiện để tránh tặng thưởng nhiều lần và tái sử dụng `RewardService` một cách chính xác. **Đánh giá: Tốt.**
- `app/services/user_engagement_tracking_service.ts`: Cấu trúc tốt, ánh xạ `TRACKING_ACTION` sang `EEngagementType` hợp lý. Logic kiểm tra và trao thưởng cho các cột mốc đã được triển khai, bao gồm cả việc kiểm tra chống trùng lặp. **Đánh giá: Tốt, nhưng chứa lỗi logic nghiêm trọng.**
- `app/services/reward_service.ts`: Là trung tâm xử lý việc cộng/trừ điểm, tích hợp với dịch vụ bên thứ ba (`SmileRewardService`). **Đánh giá: Chứa logic cốt lõi gây ra lỗi.**

## 4. Các Vấn Đề Tìm Thấy và Yêu Cầu Chỉnh Sửa (To-Be)

### 4.1. [LỖI NGHIÊM TRỌNG] Tính Toán Sai Điểm Thưởng Cho Cột Mốc Tương Tác

- **Mô tả vấn đề:** Logic hiện tại đang lấy **giá trị tiền tệ** (`rewardValue`) của một cột mốc để làm **số điểm** (`pointsChange`) trao cho người dùng, thay vì lấy **số điểm** (`points`) đã được cấu hình cho cột mốc đó.
- **Ví dụ:** Admin cấu hình "1000 views -> 100 points", nhưng backend lại trao thưởng cho người dùng 0 điểm (hoặc giá trị tiền tệ nếu có).
- **Yêu cầu chỉnh sửa:**
  1.  **File:** `app/services/user_engagement_tracking_service.ts`
      - **Phương thức:** `awardMilestoneReward`
      - **Nhiệm vụ:** Thay đổi cách gọi `rewardService`. Phải truyền `condition.points` vào `variableRewardValue`.

        ```typescript
        // Code cần sửa
        await this.rewardService.adjustPointsByProgramId({
          userId,
          rewardProgramId: condition.rewardProgramId,
          variableRewardValue: condition.rewardValue || undefined, // LỖI: Đang truyền giá trị tiền
        })

        // Code đúng
        const pointsToAward = condition.points
        if (pointsToAward > 0) {
          await this.rewardService.adjustPointsByProgramId({
            userId,
            rewardProgramId: condition.rewardProgramId,
            variableRewardValue: pointsToAward, // SỬA LẠI: Truyền đúng số điểm cần thưởng
          })

          // Cập nhật trạng thái rewardProcessed sau khi gọi service
          await ZnUserEngagementMilestone.query()
            .where('userId', userId)
            .where('milestoneConditionId', condition.id)
            .update({ rewardProcessed: true })
        }
        ```

  2.  **File:** `app/services/reward_service.ts`
      - **Phương thức:** `adjustPoint`
      - **Nhiệm vụ:** Đơn giản hóa logic tính điểm cho `ENGAGEMENT_MILESTONE` để nó trực tiếp sử dụng giá trị được truyền vào.

        ```typescript
        // Code cần sửa
        } else if (rewardProgram.programType === ERewardProgramType.ENGAGEMENT_MILESTONE) {
          pointsChange = data.variableRewardValue || rewardProgram.pointsPrice || 0 // LỖI: Logic phức tạp không cần thiết
        }

        // Code đúng
        } else if (rewardProgram.programType === ERewardProgramType.ENGAGEMENT_MILESTONE) {
          // Điểm thưởng đã được tính toán và truyền vào từ tracking service
          pointsChange = data.variableRewardValue || 0;
        }
        ```

### 4.2. [ƯU TIÊN CAO] Giao Diện (UI) Không Đồng Bộ Với Logic Backend

- **Mô tả vấn đề:** Giao diện quản trị hiện tại đang hiển thị các trường không cần thiết hoặc gây nhầm lẫn cho admin, dẫn đến việc cấu hình sai chương trình thưởng.
- **Yêu cầu chỉnh sửa (Dành cho Frontend Agent):**
  - **Rule 1: Giao diện động:** Form "Manage reward programs" phải tự động ẩn/hiện các phần tử dựa trên giá trị của dropdown "Reward program type".
  - **Rule 2: Khi `Reward program type` = "First time using app":**
    - **ẨN** toàn bộ khu vực "Earning value" (bao gồm `Points Exchange Type`, `Money Exchange Type`, `Points*`, `Reward Value*`).
    - **HIỂN THỊ** một khu vực riêng chỉ chứa một trường nhập liệu duy nhất, ví dụ: "Discount Amount ($)". Trường này tương ứng với `discountAmount` trong DB.
  - **Rule 3: Khi `Reward program type` = "Count view" (hoặc các loại tương tác khác):**
    - **ẨN** hai trường "Points\*" và "Reward Value\*" ở cấp cao nhất của khu vực "Earning value". Lý do: Logic của loại chương trình này được quyết định 100% bởi các mốc trong "Additional Conditions". Việc giữ lại các trường này sẽ làm admin hiểu nhầm rằng chúng có tác dụng.

### 4.3. [ƯU TIÊN TRUNG BÌNH] Rủi Ro Hiệu Năng

- **Mô tả vấn đề:** Phương thức `getUserEngagementCount` trong `user_engagement_tracking_service.ts` thực hiện một query `COUNT(*)` trực tiếp trên bảng `zn_trackings` mỗi khi có một hành động của người dùng được ghi lại. Bảng `zn_trackings` có thể phát triển rất lớn, làm cho truy vấn này trở thành một điểm nghẽn hiệu năng nghiêm trọng.
- **Yêu cầu chỉnh sửa (Cải tiến cho tương lai):**
  - **Bước 1: Tạo bảng tổng hợp (Summary Table):**
    - Tạo một migration mới để tạo bảng `zn_user_engagement_counts`.
    - Các cột cần có: `id`, `userId`, `engagementType`, `count`, `createdAt`, `updatedAt`.
    - Tạo index trên `(userId, engagementType)`.
  - **Bước 2: Cập nhật logic tracking:**
    - Khi một hành động của người dùng được ghi lại (ví dụ, trong `TrackingService`), ngoài việc ghi vào `zn_trackings`, hãy thực hiện một thao tác `INSERT ... ON DUPLICATE KEY UPDATE` (hoặc `UPSERT`) để tăng giá trị `count` trong bảng `zn_user_engagement_counts`.
  - **Bước 3: Tái cấu trúc (Refactor) `getUserEngagementCount`:**
    - Sửa lại phương thức `getUserEngagementCount` để nó truy vấn vào bảng `zn_user_engagement_counts` mới. Thao tác này sẽ nhanh và hiệu quả hơn rất nhiều so với việc đếm trên bảng `zn_trackings`.
